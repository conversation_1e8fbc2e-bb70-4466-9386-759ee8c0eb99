# Word格式化 MCP服务

专门用于Word文档格式化操作的MCP服务，提供样式创建、文本格式化、表格格式化等功能。

## 功能特性

### 🎨 样式与文本格式化工具
- **`create_custom_style_tool`** - 创建自定义样式
  - 支持设置加粗、斜体、字体大小、字体名称、颜色等属性
  - 可基于现有样式创建新样式
- **`format_text_tool`** - 格式化段落中的特定文本范围
  - 支持加粗、斜体、下划线、颜色、字体大小、字体名称等格式

### 📊 表格格式化工具
- **`format_table_tool`** - 使用边框、阴影和结构格式化表格
  - 支持设置标题行、边框样式、单元格阴影
- **`set_table_cell_shading_tool`** - 为特定表格单元格应用阴影/填充
  - 支持十六进制颜色和颜色名称
  - 支持多种阴影模式
- **`apply_table_alternating_rows_tool`** - 为表格应用交替行颜色
  - 提高表格可读性
  - 可自定义奇偶行颜色
- **`highlight_table_header_tool`** - 为表格标题行应用特殊高亮
  - 可自定义背景色和文字颜色

### 🔧 表格对齐与布局工具
- **`set_table_cell_alignment_tool`** - 设置特定表格单元格的文本对齐
  - 支持水平对齐：左对齐、居中、右对齐、两端对齐
  - 支持垂直对齐：顶部、居中、底部
- **`set_entire_table_alignment_tool`** - 设置整个表格所有单元格的文本对齐方式
  - 批量设置所有单元格的对齐方式
- **`set_table_column_width_tool`** - 设置特定表格列的宽度
  - 支持多种单位：磅、英寸、厘米、百分比、自动

## 工具详细说明

### 样式创建
```json
{
  "filename": "document.docx",
  "style_name": "重要提示",
  "bold": true,
  "color": "red",
  "font_size": 14
}
```

### 文本格式化
```json
{
  "filename": "document.docx",
  "paragraph_index": 1,
  "start_pos": 10,
  "end_pos": 20,
  "bold": true,
  "color": "blue"
}
```

### 表格格式化
```json
{
  "filename": "document.docx",
  "table_index": 0,
  "has_header_row": true,
  "border_style": "single"
}
```

### 表格对齐设置
```json
{
  "filename": "document.docx",
  "table_index": 0,
  "horizontal": "center",
  "vertical": "center"
}
```

## 使用示例

- "创建名为'重要提示'的自定义样式，加粗红色字体"
- "将第2段第10-20字符设为加粗蓝色"
- "格式化第一个表格，添加边框和标题行"
- "设置表格第一行为蓝色背景"
- "设置整个表格文本居中对齐"
- "设置表格第一列宽度为2英寸"
- "为表格应用交替行颜色"

## 技术特性

### 🔧 核心功能
- **样式管理**：创建和应用自定义样式
- **精确格式化**：基于位置的文本格式化
- **表格美化**：完整的表格格式化解决方案
- **批量操作**：支持整个表格的批量设置

### 📋 支持的格式
- **文本格式**：加粗、斜体、下划线、颜色、字体
- **表格样式**：边框、阴影、交替行、标题高亮
- **对齐方式**：水平（左/中/右/两端）、垂直（上/中/下）
- **尺寸单位**：磅、英寸、厘米、百分比、自动

### 🛡️ 安全特性
- **文件检查**：自动验证文件存在性和可写性
- **参数验证**：严格的输入参数类型和范围检查
- **错误处理**：完整的异常捕获和友好的错误信息
- **备份建议**：在无法写入时提示创建副本

## 安装与运行

### 依赖要求
```bash
pip install python-docx fastmcp
```

### 启动服务
```bash
python -m word_formatting.main
```

### 配置说明
- 服务使用 stdio 传输协议
- 支持 FastMCP 框架
- 自动设置日志级别为 INFO

## 注意事项

1. **文件格式**：仅支持 .docx 格式的 Word 文档
2. **文件权限**：确保对目标文档有读写权限
3. **索引从0开始**：所有索引参数（段落、表格、行、列）都从0开始计数
4. **颜色格式**：支持颜色名称（如 "red"）和十六进制值（如 "FF0000"）
5. **备份建议**：建议在格式化前备份重要文档
