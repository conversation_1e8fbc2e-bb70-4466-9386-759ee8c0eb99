"""
Word格式化 MCP服务主程序

提供Word文档的格式化功能，包括样式创建、文本格式化、表格格式化等。
"""
import os
import sys

# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    create_custom_style,
    format_text,
    format_table,
    set_table_cell_shading,
    apply_table_alternating_rows,
    highlight_table_header,
    set_table_cell_alignment,
    set_table_column_width,
    set_entire_table_alignment
)



# 初始化FastMCP服务器
mcp = FastMCP("Word格式化服务")

def register_tools():
    """使用FastMCP装饰器注册所有工具"""

    @mcp.tool()
    def create_custom_style_tool(filename: str, style_name: str, bold: bool = None,
                                italic: bool = None, font_size: int = None,
                                font_name: str = None, color: str = None,
                                base_style: str = None):
        """在文档中创建自定义样式"""
        return create_custom_style(filename, style_name, bold, italic, font_size, font_name, color, base_style)

    @mcp.tool()
    def format_text_tool(filename: str, paragraph_index: int, start_pos: int, end_pos: int,
                        bold: bool = None, italic: bool = None, underline: bool = None,
                        color: str = None, font_size: int = None, font_name: str = None):
        """格式化段落中的特定文本范围"""
        return format_text(filename, paragraph_index, start_pos, end_pos, bold, italic, underline, color, font_size, font_name)

    @mcp.tool()
    def format_table_tool(filename: str, table_index: int, has_header_row: bool = None,
                         border_style: str = None, shading: list = None):
        """使用边框、阴影和结构格式化表格"""
        return format_table(filename, table_index, has_header_row, border_style, shading)

    @mcp.tool()
    def apply_table_alternating_rows_tool(filename: str, table_index: int,
                                         color1: str = "FFFFFF", color2: str = "F2F2F2"):
        """为表格应用交替行颜色以提高可读性"""
        return apply_table_alternating_rows(filename, table_index, color1, color2)

    @mcp.tool()
    def highlight_table_header_tool(filename: str, table_index: int,
                                   header_color: str = "4472C4", text_color: str = "FFFFFF"):
        """为表格标题行应用特殊高亮"""
        return highlight_table_header(filename, table_index, header_color, text_color)

    @mcp.tool()
    def set_table_cell_alignment_tool(filename: str, table_index: int, row_index: int, col_index: int,
                                     horizontal: str = "left", vertical: str = "top"):
        """设置特定表格单元格的文本对齐"""
        return set_table_cell_alignment(filename, table_index, row_index, col_index, horizontal, vertical)

    @mcp.tool()
    def set_table_cell_shading_tool(filename: str, table_index: int, row_index: int,
                                   col_index: int, fill_color: str, pattern: str = "clear"):
        """为特定表格单元格应用阴影/填充"""
        return set_table_cell_shading(filename, table_index, row_index, col_index, fill_color, pattern)

    @mcp.tool()
    def set_table_column_width_tool(filename: str, table_index: int, col_index: int,
                                   width: float, width_type: str = "points"):
        """设置特定表格列的宽度"""
        return set_table_column_width(filename, table_index, col_index, width, width_type)

    @mcp.tool()
    def set_entire_table_alignment_tool(filename: str, table_index: int,
                                       horizontal: str = "left", vertical: str = "top"):
        """设置整个表格所有单元格的文本对齐方式"""
        return set_entire_table_alignment(filename, table_index, horizontal, vertical)

def main():
    """服务器的主入口点 - 只支持stdio传输"""
    # 注册所有工具
    register_tools()

    print("启动Word格式化MCP服务器...")

    try:
        # 只使用stdio传输运行
        mcp.run(transport='stdio')
    except KeyboardInterrupt:
        print("\n正在关闭Word格式化服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
